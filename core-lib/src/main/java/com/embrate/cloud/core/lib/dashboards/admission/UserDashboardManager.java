package com.embrate.cloud.core.lib.dashboards.admission;

import com.embrate.cloud.core.api.dashboards.admission.StudentAdmTCCount;
import com.embrate.cloud.core.api.dashboards.admission.StudentAdmissionOrgStats;
import com.embrate.cloud.core.api.dashboards.admission.StudentBirthdayInfo;
import com.embrate.cloud.core.api.dashboards.admission.StudentBirthdayStats;
import com.embrate.cloud.core.api.dashboards.admission.StudentTransportCount;
import com.embrate.cloud.core.api.dashboards.admission.StudentTransportOrgStats;
import com.embrate.cloud.core.api.dashboards.common.InstituteValue;
import com.embrate.cloud.dao.tier.dashboard.admission.UserDashboardDao;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.StudentSessionSummary;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportAssignmentManager;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.embrate.cloud.core.utils.institute.DashboardUtils.*;

/**
 * <AUTHOR>
 **/
public class UserDashboardManager {

	private static final Logger logger = LogManager.getLogger(UserDashboardManager.class);

	private final InstituteManager instituteManager;

	private final UserDashboardDao userDashboardDao;

	private final TransportAssignmentManager transportAssignmentManager;

	public UserDashboardManager(InstituteManager instituteManager, UserDashboardDao userDashboardDao, TransportAssignmentManager transportAssignmentManager) {
		this.instituteManager = instituteManager;
		this.userDashboardDao = userDashboardDao;
		this.transportAssignmentManager = transportAssignmentManager;
	}

	public StudentAdmissionOrgStats getStudentAdmissionOrgStats(UUID organizationId, String selectedInstituteIds,
																UUID userId, int startDate, int endDate) {

		validatePayload(organizationId, selectedInstituteIds, userId, startDate, endDate);
		List<Integer> institutes = getInstituteIds(selectedInstituteIds);
		List<StudentAdmTCCount> newAdmissionsAndTCCountList = userDashboardDao.getNewAdmissionsAndTCCountByDateRange(institutes, startDate, endDate);
		Map<Integer, String> instituteNameMap = getInstituteBranchNameMap(instituteManager.getInstitutes(institutes));

		List<InstituteValue> newAdmissions = new ArrayList<>();
		List<InstituteValue> tcIssued = new ArrayList<>();
		int totalNewAdmission = 0;
		int totalTCIssued = 0;

		// Create a map to track which institutes have data
		Map<Integer, StudentAdmTCCount> instituteDataMap = new HashMap<>();
		for (StudentAdmTCCount studentAdmTCCount : newAdmissionsAndTCCountList) {
			instituteDataMap.put(studentAdmTCCount.getInstituteId(), studentAdmTCCount);
			totalNewAdmission += studentAdmTCCount.getAdmissionCount();
			totalTCIssued += studentAdmTCCount.getRelieveCount();
		}

		// Create InstituteValue objects for all institutes, with zero values for institutes without data
		for (Integer institute : institutes) {
			StudentAdmTCCount data = instituteDataMap.get(institute);
			if (data != null) {
				newAdmissions.add(new InstituteValue(data.getInstituteId(), instituteNameMap.get(data.getInstituteId()), data.getAdmissionCount()));
				tcIssued.add(new InstituteValue(data.getInstituteId(), instituteNameMap.get(data.getInstituteId()), data.getRelieveCount()));
			} else {
				// Create entries with zero values for institutes without data
				newAdmissions.add(new InstituteValue(institute, instituteNameMap.get(institute), 0));
				tcIssued.add(new InstituteValue(institute, instituteNameMap.get(institute), 0));
			}
		}

		return new StudentAdmissionOrgStats(newAdmissions, totalNewAdmission, tcIssued, totalTCIssued);
	}

	public StudentBirthdayStats getStudentBirthdayStats(UUID organizationId, String selectedInstituteIds, UUID userId, int overrideDate) {
		validateBirthdayPayload(organizationId, selectedInstituteIds, userId);
		List<Integer> institutes = getInstituteIds(selectedInstituteIds);

		// Get current academic sessions for the institutes
		Set<Integer> currentAcademicSessions = new HashSet<>();
		for (Integer instituteId : institutes) {
			AcademicSession currentSession = instituteManager.getCurrentDateSessionDetails(instituteId);
			if (currentSession != null) {
				currentAcademicSessions.add(currentSession.getAcademicSessionId());
			}
		}

		// Get all enrolled students with their current session and class information
		List<StudentBirthdayInfo> allStudents = userDashboardDao.getAllEnrolledStudentsWithCurrentSession(institutes, currentAcademicSessions);

		List<StudentBirthdayInfo> todayBirthdays = new ArrayList<>();
		List<StudentBirthdayInfo> upcomingBirthdays = new ArrayList<>();

		// Get current date information for birthday computation
		int currentDate = overrideDate > 0 ? overrideDate : DateUtils.now();
		int currentYear = DateUtils.getDefaultZoneYear(currentDate);

		// Compute birthday filtering in application code
		for (StudentBirthdayInfo student : allStudents) {
			Integer birthdayDate = student.getBirthdayDate();
			if (birthdayDate == null || birthdayDate <= 0) {
				continue;
			}

			// Calculate this year's birthday
			int birthMonth = DateUtils.getIntMonthOfYear(birthdayDate);
			int birthDay = DateUtils.getIntDayOfMonth(birthdayDate);

			// Create this year's birthday date using the same approach as existing birthday logic
			// EMonthDay constructor takes (day, month)
			int thisYearBirthday = DateUtils.getDefaultZoneTime(new com.lernen.cloud.core.api.common.EMonthDay(birthDay, birthMonth), currentYear);

			// Check if birthday is today
			if (DateUtils.isSameDay(currentDate, thisYearBirthday)) {
				todayBirthdays.add(student);
			}
			// Check if birthday is in the next 7 days
			else {
				int daysBetween = DateUtils.daysBetween(currentDate, thisYearBirthday);
				if (daysBetween > 0 && daysBetween <= 7) {
					upcomingBirthdays.add(student);
				}
				// Handle year-end wrap around (e.g., current date is Dec 28, birthday is Jan 2)
				else if (daysBetween < 0) {
					// Birthday already passed this year, check next year
					int nextYearBirthday = DateUtils.getDefaultZoneTime(new com.lernen.cloud.core.api.common.EMonthDay(birthDay, birthMonth), currentYear + 1);
					int daysToNextYearBirthday = DateUtils.daysBetween(currentDate, nextYearBirthday);
					if (daysToNextYearBirthday > 0 && daysToNextYearBirthday <= 7) {
						upcomingBirthdays.add(student);
					}
				}
			}
		}

		return new StudentBirthdayStats(todayBirthdays, upcomingBirthdays);
	}

	public StudentTransportOrgStats getStudentTransportStats(UUID organizationId, String selectedInstituteIds, UUID userId, int date) {
		validateTransportPayload(organizationId, selectedInstituteIds, userId, date);
		List<Integer> institutes = getInstituteIds(selectedInstituteIds);
		Map<Integer, String> instituteNameMap = getInstituteBranchNameMap(instituteManager.getInstitutes(institutes));

		// Get current academic sessions for the institutes
		Map<Integer, List<AcademicSession>> instituteSessionMap = instituteManager.getInstituteAcademicSessionMap(institutes);
		Set<Integer> currentAcademicSessions = getRequiredSessions(instituteSessionMap, date, date);

		if (CollectionUtils.isEmpty(currentAcademicSessions)) {
			logger.warn("No academic sessions found for institutes {} and date {}", institutes, date);
			// Return objects with zero values for all institutes instead of empty arrays
			return createEmptyTransportStatsWithZeroValues(institutes, instituteNameMap);
		}

		// Get enrolled students count for each institute and session
		List<StudentSessionSummary> sessionSummaryList = userDashboardDao.getStudentCountBySessionStatus(institutes, currentAcademicSessions);
		if (CollectionUtils.isEmpty(sessionSummaryList)) {
			logger.warn("No enrolled students found for institutes {} and sessions {}", institutes, currentAcademicSessions);
			return createEmptyTransportStatsWithZeroValues(institutes, instituteNameMap);
		}

		// Filter only enrolled students
		Map<Integer, Integer> instituteEnrolledCountMap = new HashMap<>();
		for (StudentSessionSummary summary : sessionSummaryList) {
			if (summary.getSessionStatus() == StudentStatus.ENROLLED) {
				instituteEnrolledCountMap.put(summary.getInstituteId(),
					instituteEnrolledCountMap.getOrDefault(summary.getInstituteId(), 0) + summary.getStudentCount());
			}
		}

		// Get transport assigned students count for each institute and session
		Map<Integer, Integer> instituteTransportAssignedCountMap = new HashMap<>();
		Map<Integer, Integer> instituteToSessionMap = new HashMap<>();

		// Create institute to session mapping
		for (StudentSessionSummary summary : sessionSummaryList) {
			if (summary.getSessionStatus() == StudentStatus.ENROLLED) {
				instituteToSessionMap.put(summary.getInstituteId(), summary.getAcademicSessionId());
			}
		}

		// Get transport assigned counts
		for (Map.Entry<Integer, Integer> entry : instituteToSessionMap.entrySet()) {
			int instituteId = entry.getKey();
			int academicSessionId = entry.getValue();
			int transportAssignedCount = transportAssignmentManager.getTransportActiveStudentCount(instituteId, academicSessionId);
			instituteTransportAssignedCountMap.put(instituteId, transportAssignedCount);
		}

		// Build response lists
		List<InstituteValue> enrolledStudents = new ArrayList<>();
		List<InstituteValue> transportAssignedStudents = new ArrayList<>();
		List<InstituteValue> transportUnassignedStudents = new ArrayList<>();

		int totalEnrolledStudents = 0;
		int totalTransportAssignedStudents = 0;
		int totalTransportUnassignedStudents = 0;

		for (Integer instituteId : institutes) {
			String instituteName = instituteNameMap.get(instituteId);
			int enrolledCount = instituteEnrolledCountMap.getOrDefault(instituteId, 0);
			int transportAssignedCount = instituteTransportAssignedCountMap.getOrDefault(instituteId, 0);
			int transportUnassignedCount = enrolledCount - transportAssignedCount;

			enrolledStudents.add(new InstituteValue(instituteId, instituteName, enrolledCount));
			transportAssignedStudents.add(new InstituteValue(instituteId, instituteName, transportAssignedCount));
			transportUnassignedStudents.add(new InstituteValue(instituteId, instituteName, transportUnassignedCount));

			totalEnrolledStudents += enrolledCount;
			totalTransportAssignedStudents += transportAssignedCount;
			totalTransportUnassignedStudents += transportUnassignedCount;
		}

		return new StudentTransportOrgStats(enrolledStudents, totalEnrolledStudents,
			transportAssignedStudents, totalTransportAssignedStudents,
			transportUnassignedStudents, totalTransportUnassignedStudents);
	}

	private void validateTransportPayload(UUID organizationId, String selectedInstituteIds, UUID userId, int date) {
		if (organizationId == null) {
			throw new RuntimeException("Invalid organization id.");
		}
		if (selectedInstituteIds == null || selectedInstituteIds.trim().isEmpty()) {
			throw new RuntimeException("Invalid institute ids.");
		}
		if (userId == null) {
			throw new RuntimeException("Invalid user id.");
		}
		if (date <= 0) {
			throw new RuntimeException("Invalid date.");
		}
	}

	private StudentTransportOrgStats createEmptyTransportStatsWithZeroValues(List<Integer> institutes, Map<Integer, String> instituteNameMap) {
		List<InstituteValue> enrolledStudents = new ArrayList<>();
		List<InstituteValue> transportAssignedStudents = new ArrayList<>();
		List<InstituteValue> transportUnassignedStudents = new ArrayList<>();

		for (Integer instituteId : institutes) {
			String instituteName = instituteNameMap.get(instituteId);
			enrolledStudents.add(new InstituteValue(instituteId, instituteName, 0));
			transportAssignedStudents.add(new InstituteValue(instituteId, instituteName, 0));
			transportUnassignedStudents.add(new InstituteValue(instituteId, instituteName, 0));
		}

		return new StudentTransportOrgStats(enrolledStudents, 0, transportAssignedStudents, 0, transportUnassignedStudents, 0);
	}

	private void validateBirthdayPayload(UUID organizationId, String selectedInstituteIds, UUID userId) {
		if (organizationId == null) {
			throw new RuntimeException("Invalid organization id.");
		}
		if (selectedInstituteIds == null || selectedInstituteIds.trim().isEmpty()) {
			throw new RuntimeException("Invalid institute ids.");
		}
		if (userId == null) {
			throw new RuntimeException("Invalid user id.");
		}
	}
}