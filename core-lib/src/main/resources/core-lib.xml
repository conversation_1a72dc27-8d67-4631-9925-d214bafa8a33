<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-3.0.xsd" default-lazy-init="true">

    <context:annotation-config/>
    <context:property-placeholder
            location="classpath*:core-lib-${lernen_env}.properties, classpath*:dao-tier-${lernen_env}.properties, classpath*:utils-${lernen_env}.properties"
            system-properties-mode="OVERRIDE"/>

    <import resource="classpath:dao-tier.xml"/>
    <import resource="classpath:core-utils.xml"/>

    <bean id="helloWorld" class="com.lernen.cloud.core.lib.HelloWorld">
        <constructor-arg name="message" value="Yup Working!!!"/>
    </bean>

    <bean id="userManager" class="com.lernen.cloud.core.lib.managers.UserManager">
        <constructor-arg name="userDao" ref="userDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="studentDao" ref="studentDao"/>
        <constructor-arg name="userPermissionDao" ref="userPermissionDao"/>
        <constructor-arg name="staffDao" ref="staffDao"/>
        <constructor-arg name="mobileApplicationManager" ref="mobileApplicationManager"/>
        <constructor-arg name="trackingEventsManager" ref="trackingEventsManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="defaultOauthTokenProvider" ref="defaultOauthTokenProvider"/>
        <constructor-arg name="mobileAppInstituteScopeFactory" ref="mobileAppInstituteScopeFactory"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
    </bean>

    <bean id="libraryManager" class="com.lernen.cloud.core.lib.managers.LibraryManager">
        <constructor-arg name="libraryDao" ref="libraryDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="assessmentDetailsManager" class="com.lernen.cloud.core.lib.assessment.AssessmentDetailsManager">
        <constructor-arg name="assessmentDao" ref="assessmentDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="assessmentExcelUtils" ref="assessmentExcelUtils"/>
        <constructor-arg name="assessmentMarksCaculatorHandler" ref="assessmentMarksCaculatorHandler"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
    </bean>
    <bean id="assessmentExcelUtils" class="com.lernen.cloud.core.lib.assessment.AssessmentExcelUtils">
        <constructor-arg name="documentManager" ref="documentManager"/>
    </bean>
    <bean id="assessmentMarksCaculatorHandler"
		class="com.lernen.cloud.core.lib.assessment.calculator.AssessmentMarksCaculatorHandler">
		<constructor-arg name="assessmentMarksCaculatorFactory" ref="assessmentMarksCaculatorFactory" />
	</bean>
    <bean id="assessmentMarksCaculatorFactory"
		class="com.lernen.cloud.core.lib.assessment.calculator.AssessmentMarksCaculatorFactory">
	</bean>
    <bean id="inventoryOutletManager"
          class="com.embrate.cloud.core.lib.inventory.v2.InventoryOutletManager">
        <constructor-arg name="inventoryOutletDao" ref="inventoryOutletDao"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>


    <bean id="storeInventoryManager"
          class="com.lernen.cloud.core.lib.inventory.StoreInventoryManager">
        <constructor-arg name="storeInventoryDao" ref="storeInventoryDao"/>
        <constructor-arg name="productDao" ref="productDao"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="inventoryProductManager"
          class="com.embrate.cloud.core.lib.inventory.v2.InventoryProductManager">
        <constructor-arg name="inventoryOutletDao" ref="inventoryOutletDao"/>
        <constructor-arg name="inventoryProductDao" ref="inventoryProductDao"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
    </bean>

    <bean id="productTransactionsManager"
          class="com.lernen.cloud.core.lib.inventory.ProductTransactionsManager">
        <constructor-arg name="productTransactionsDao" ref="productTransactionsDao"/>
        <constructor-arg name="storeInventoryDao" ref="storeInventoryDao"/>
        <constructor-arg name="productGroupManager" ref="productGroupManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
    </bean>

    <bean id="inventoryTransactionsManager"
          class="com.embrate.cloud.core.lib.inventory.v2.InventoryTransactionsManager">
        <constructor-arg name="inventoryTransactionsDao" ref="inventoryTransactionsDao"/>
        <constructor-arg name="inventoryOutletDao" ref="inventoryOutletDao"/>
        <constructor-arg name="productGroupManager" ref="productGroupManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
    </bean>

    <bean id="reportGenerationManager"
          class="com.lernen.cloud.core.lib.inventory.reports.ReportGenerationManager">
        <constructor-arg name="productTransactionsDao" ref="productTransactionsDao"/>
        <constructor-arg name="storeInventoryManager" ref="storeInventoryManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
    </bean>

    <bean id="inventoryMigrationManager"
          class="com.embrate.cloud.core.lib.inventory.v2.InventoryMigrationManager">
        <constructor-arg name="inventoryOutletDao" ref="inventoryOutletDao"/>
        <constructor-arg name="storeInventoryManager" ref="storeInventoryManager"/>
        <constructor-arg name="inventoryProductDao" ref="inventoryProductDao"/>
        <constructor-arg name="productDao" ref="productDao"/>
        <constructor-arg name="inventoryTransactionsDao" ref="inventoryTransactionsDao"/>
        <constructor-arg name="productTransactionsDao" ref="productTransactionsDao"/>
        <constructor-arg name="inventoryMigrationDao" ref="inventoryMigrationDao"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="inventorySetupProcessor" ref="inventorySetupProcessor"/>
    </bean>

    <bean id="inventorySetupProcessor"
          class="com.embrate.cloud.core.lib.institute.onboarding.step.InventorySetupProcessor">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="configurationManager" ref="configurationManager"/>
        <constructor-arg name="inventoryOutletManager" ref="inventoryOutletManager"/>
    </bean>

    <bean id="leaveModuleSetupProcessor"
          class="com.embrate.cloud.core.lib.institute.onboarding.step.LeaveModuleSetupProcessor">
        <constructor-arg name="leaveConfigurationManager" ref="leaveConfigurationManager"/>
    </bean>

    <bean id="salaryManagementSetupProcessor"
          class="com.embrate.cloud.core.lib.institute.onboarding.step.SalaryManagementSetupProcessor">
        <constructor-arg name="salaryConfigurationManager" ref="salaryConfigurationManager"/>
    </bean>

    <bean id="complaintBoxSetupProcessor"
          class="com.embrate.cloud.core.lib.institute.onboarding.step.ComplaintBoxSetupProcessor">
        <constructor-arg name="complainBoxManager" ref="complainBoxManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
    </bean>

    <bean id="userManagementModuleSetupProcessor"
          class="com.embrate.cloud.core.lib.institute.onboarding.step.UserManagementModuleSetupProcessor">
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
    </bean>

    <!-- <bean id="stockIngester" class="com.lernen.cloud.core.lib.inventory.ingest.StockIngester">
        <constructor-arg name="storeInventoryManager" ref="storeInventoryManager"/>
        </bean> -->

    <bean id="instituteManager" class="com.lernen.cloud.core.lib.institute.InstituteManager">
        <constructor-arg name="instituteDao" ref="instituteDao"/>
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="examinationDao" ref="examinationDao"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="userDao" ref="userDao"/>
        <constructor-arg name="assetProvider" ref="assetProvider"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="documentManager" ref="frontendAssetManager"/>
        <constructor-arg name="salaryDao" ref="salaryDao"/>
        <constructor-arg name="attendanceDao" ref="attendanceDao"/>
        <constructor-arg name="staffDao" ref="staffDao"/>
    </bean>

    <bean id="assetProvider" class="com.embrate.cloud.core.lib.utility.AssetProvider">
        <constructor-arg name="instituteDao" ref="instituteDao"/>
        <constructor-arg name="documentManager" ref="frontendAssetManager"/>
        <constructor-arg name="cacheFactory" ref="cacheFactory"/>
    </bean>

    <bean id="frontendAssetManager" class="com.lernen.cloud.core.lib.document.DocumentManager">
        <constructor-arg name="s3FileSystem" ref="s3FileSystem"/>
        <constructor-arg name="s3AccessEnable" value="${document.upload.access}"/>
        <constructor-arg name="bucketName" value="${fe.asset.bucket.name}"/>
    </bean>


    <bean id="instituteManagementManager" class="com.lernen.cloud.core.lib.institute.InstituteManagementManager">
        <constructor-arg name="instituteDao" ref="instituteDao"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
    </bean>

    <bean id="productGroupManager" class="com.lernen.cloud.core.lib.inventory.ProductGroupManager">
        <constructor-arg name="productGroupDao" ref="productGroupDao"/>
    </bean>

    <bean id="inventoryProductGroupManager"
          class="com.embrate.cloud.core.lib.inventory.v2.InventoryProductGroupManager">
        <constructor-arg name="inventoryProductGroupDao" ref="inventoryProductGroupDao"/>
        <constructor-arg name="inventoryOutletDao" ref="inventoryOutletDao"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="feeConfigurationManager"
          class="com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager">
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
        <constructor-arg name="feeCalculator" ref="feeCalculator"/>
        <constructor-arg name="feePaymentManager" ref="feePaymentManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="feePaymentTransactionDao" ref="feePaymentTransactionDao"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="feeAuditLogWriter" ref="feeAuditLogWriter"/>
    </bean>

    <bean id="feeDiscountConfigurationManager"
          class="com.lernen.cloud.core.lib.fees.configuration.FeeDiscountConfigurationManager">
        <constructor-arg name="feeDiscountConfigurationDao" ref="feeDiscountConfigurationDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="feeCalculator" ref="feeCalculator"/>
    </bean>

    <bean id="studentManager" class="com.lernen.cloud.core.lib.student.StudentManager">
        <constructor-arg name="studentDao" ref="studentDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="studentAuditLogWriter" ref="studentAuditLogWriter"/>
        <constructor-arg name="attendanceManager" ref="attendanceManager"/>
        <constructor-arg name="feePaymentTransactionDao" ref="feePaymentTransactionDao"/>
        <constructor-arg name="staffDao" ref="staffDao"/>
        <constructor-arg name="hostelManagementDao" ref="hostelManagementDao"/>

    </bean>

    <bean id="studentAdmissionManager"
          class="com.lernen.cloud.core.lib.student.StudentAdmissionManager">
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="feePaymentManager" ref="feePaymentManager"/>
        <constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="transportAssignmentManager"
                         ref="transportAssignmentManager"/>
        <constructor-arg name="transportFeeConfigurationManager"
                         ref="transportFeeConfigurationManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="transportConfigurationDao" ref="transportConfigurationDao"/>
        <constructor-arg name="feeDiscountConfigurationManager" ref="feeDiscountConfigurationManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="examinationManager" ref="examinationManager"/>
        <constructor-arg name="courseManager" ref="courseManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="examReportCardManager" ref="examReportCardManager"/>
        <constructor-arg name="transportConfigurationManager" ref="transportConfigurationManager"/>
        <constructor-arg name="attendanceManager" ref="attendanceManager"/>
        <constructor-arg name="noticeBoardManager" ref="noticeBoardManager"/>
        <constructor-arg name="studentDiaryManager" ref="studentDiaryManager"/>
        <constructor-arg name="homeworkManager" ref="homeworkManager"/>
        <constructor-arg name="holidayCalendarManager" ref="holidayCalendarManager"/>
    </bean>


    <bean id="feePaymentManager"
          class="com.lernen.cloud.core.lib.fees.payment.FeePaymentManager">
        <constructor-arg name="feePaymentTransactionDao" ref="feePaymentTransactionDao"/>
        <constructor-arg name="feeDiscountConfigurationDao"
                         ref="feeDiscountConfigurationDao"/>
        <constructor-arg name="feeCalculator" ref="feeCalculator"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="feePaymentInsightManager" ref="feePaymentInsightManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="feeAuditLogWriter" ref="feeAuditLogWriter"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="userWalletManager" ref="userWalletManager"/>
        <constructor-arg name="scratchCardManager" ref="scratchCardManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="paymentDiscountManager" ref="paymentDiscountManager"/>
        <constructor-arg name="paymentFineManager" ref="paymentFineManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
    </bean>

    <bean id="followUpManager"
          class="com.lernen.cloud.core.lib.followup.FollowUpManager">
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="followUpDao" ref="followUpDao"/>
        <constructor-arg name="pushNotificationManager" ref="pushNotificationManager"/>
        <constructor-arg name="notificationManager" ref="notificationManager"/>
    </bean>

    <bean id="feePaymentInsightManager"
          class="com.lernen.cloud.core.lib.fees.payment.FeePaymentInsightManager">
        <constructor-arg name="feePaymentInsightsDao" ref="feePaymentInsightsDao"/>
        <constructor-arg name="feePaymentTransactionDao" ref="feePaymentTransactionDao"/>
        <constructor-arg name="feeCalculator" ref="feeCalculator"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="feeDiscountConfigurationManager" ref="feeDiscountConfigurationManager"/>
    </bean>

    <bean id="documentManager" class="com.lernen.cloud.core.lib.document.DocumentManager">
        <constructor-arg name="s3FileSystem" ref="s3FileSystem"/>
        <constructor-arg name="s3AccessEnable" value="${document.upload.access}"/>
        <constructor-arg name="bucketName" value="${document.bucket.name}"/>
    </bean>

    <!--	<bean id="feePaymentReportGenerator"-->
    <!--		class="com.lernen.cloud.core.lib.fees.payment.FeePaymentReportGenerator">-->
    <!--		<constructor-arg name="feePaymentInsightManager" ref="feePaymentInsightManager" />-->
    <!--		<constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager" />-->
    <!--		<constructor-arg name="feeDiscountConfigurationManager" ref="feeDiscountConfigurationManager" />-->
    <!--		<constructor-arg name="userManager" ref="userManager" />-->
    <!--		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />-->
    <!--		<constructor-arg name="userWalletReportsManager" ref="userWalletReportsManager" />-->
    <!--		<constructor-arg name="instituteManager" ref="instituteManager" />-->
    <!--	</bean>-->

    <bean id="feeCalculator" class="com.lernen.cloud.core.lib.fees.payment.FeeCalculator">
        <constructor-arg name="feePaymentTransactionDao" ref="feePaymentTransactionDao"/>
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
        <constructor-arg name="feeDiscountConfigurationDao"
                         ref="feeDiscountConfigurationDao"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="fineAmountCalculatorFactory"
                         ref="fineAmountCalculatorFactory"/>
    </bean>

    <bean id="transportConfigurationManager"
          class="com.lernen.cloud.core.lib.transport.configuration.TransportConfigurationManager">
        <constructor-arg name="transportConfigurationDao" ref="transportConfigurationDao"/>
        <constructor-arg name="transportAssignmentDao" ref="transportAssignmentDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
    </bean>

    <bean id="studentDiaryManager" class="com.lernen.cloud.core.lib.diary.StudentDiaryManager">
        <constructor-arg name="userDiaryManager" ref="userDiaryManager"/>
    </bean>

    <bean id="staffDiaryManager" class="com.lernen.cloud.core.lib.diary.StaffDiaryManager">
        <constructor-arg name="userDiaryManager" ref="userDiaryManager"/>
    </bean>

    <bean id="userDiaryManager" class="com.lernen.cloud.core.lib.diary.UserDiaryManager">
        <constructor-arg name="userDiaryDao" ref="userDiaryDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
    </bean>

    <bean id="complainBoxManager"
          class="com.lernen.cloud.core.lib.complainbox.ComplainBoxManager">
        <constructor-arg name="complainBoxDao" ref="complainBoxDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
    </bean>

    <bean id="transportFeeConfigurationManager"
          class="com.lernen.cloud.core.lib.transport.configuration.TransportFeeConfigurationManager">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager"/>
        <constructor-arg name="transportAssignmentDao" ref="transportAssignmentDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
    </bean>

    <bean id="transportAssignmentManager"
          class="com.lernen.cloud.core.lib.transport.configuration.TransportAssignmentManager">
        <constructor-arg name="transportAssignmentDao" ref="transportAssignmentDao"/>
        <constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="feeCalculator" ref="feeCalculator"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="transportConfigurationManager" ref="transportConfigurationManager"/>
        <constructor-arg name="transportFeeConfigurationManager" ref="transportFeeConfigurationManager"/>
    </bean>


    <bean id="velocityTemplateVersionManager"
          class="com.lernen.cloud.core.lib.templates.VelocityTemplateVersionManager">
        <constructor-arg name="communicationTemplateDao"
                         ref="communicationTemplateDao"/>
    </bean>


    <bean id="transportReportsGenerator"
          class="com.lernen.cloud.core.lib.reports.transport.TransportReportsGenerator">
        <constructor-arg name="transportAssignmentManager"
                         ref="transportAssignmentManager"/>
        <constructor-arg name="transportConfigurationManager"
                         ref="transportConfigurationManager"/>
        <constructor-arg name="instituteManager"
                         ref="instituteManager"/>
        <constructor-arg name="studentManager"
                         ref="studentManager"/>
        <constructor-arg name="transportFeeConfigurationManager"
                         ref="transportFeeConfigurationManager"/>
        <constructor-arg name="userPermissionManager"
                         ref="userPermissionManager"/>
    </bean>

    <bean id="studentReportsGenerator"
          class="com.lernen.cloud.core.lib.student.StudentReportsGenerator">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
    </bean>

    <bean id="studentManagementReportsGenerator"
          class="com.lernen.cloud.core.lib.student.StudentManagementReportsGenerator">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
    </bean>


    <bean id="configurationManager" class="com.lernen.cloud.core.lib.configs.ConfigurationManager">
        <constructor-arg name="configurationDao" ref="configurationDao"/>
    </bean>


    <bean id="userPreferenceSettings"
          class="com.lernen.cloud.core.lib.configs.UserPreferenceSettings">
        <constructor-arg name="configurationManager" ref="configurationManager"/>
    </bean>

    <bean id="courseManager" class="com.embrate.cloud.core.lib.courses.CourseManager">
        <constructor-arg name="courseDao" ref="courseDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="examinationDao" ref="examinationDao"/>
        <constructor-arg name="timetableDao" ref="timetableDao"/>
        <constructor-arg name="homeworkDao" ref="homeworkDao"/>
    </bean>

    <bean id="courseReportGenerator" class="com.embrate.cloud.core.lib.courses.CourseReportGenerator">
        <constructor-arg name="courseManager" ref="courseManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="courseConfigManager" class="com.embrate.cloud.core.lib.courses.CourseConfigManager">
        <constructor-arg name="courseManager" ref="courseManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>


    <bean id="examinationManager"
          class="com.lernen.cloud.core.lib.examination.ExaminationManager">
        <constructor-arg name="examinationDao" ref="examinationDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="courseManager" ref="courseManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="googleRecaptchaManager" ref="googleRecaptchaManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="marksComputationFactory" ref="marksComputationFactory"/>
        <constructor-arg name="timetableManager" ref="timetableManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="examinationStructureHelper"
          class="com.lernen.cloud.core.lib.examination.ExaminationStructureHelper">
        <constructor-arg name="examinationManager" ref="examinationManager"/>
        <constructor-arg name="courseManager" ref="courseManager"/>
    </bean>


    <bean id="examReportCardManager"
          class="com.lernen.cloud.core.lib.examination.ExamReportCardManager">
        <constructor-arg name="examinationDao" ref="examinationDao"/>
        <constructor-arg name="courseManager" ref="courseManager"/>
        <constructor-arg name="examinationManager" ref="examinationManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="rankCalculatorFactory" ref="rankCalculatorFactory"/>
    </bean>

    <bean id="zeroFineAmountCalculator"
          class="com.lernen.clould.core.lib.fees.fine.ZeroFineAmountCalculator">
    </bean>

    <bean id="fixedFineAmountCalculator"
          class="com.lernen.clould.core.lib.fees.fine.FixedFineAmountCalculator">
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
    </bean>

    <bean id="fineAmountCalculator10205"
          class="com.lernen.clould.core.lib.fees.fine.FineAmountCalculator10205">
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
    </bean>

    <bean id="perDayFineAmountCalculator"
          class="com.lernen.clould.core.lib.fees.fine.PerDayFineAmountCalculator">
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
    </bean>


    <bean id="fineAmountCalculator10350"
          class="com.lernen.clould.core.lib.fees.fine.FineAmountCalculator10350">
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
    </bean>

    <bean id="fineAmountCalculator10360"
          class="com.lernen.clould.core.lib.fees.fine.FineAmountCalculator10360">
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
    </bean>

    <bean id="fineAmountCalculatorFactory"
          class="com.lernen.clould.core.lib.fees.fine.FineAmountCalculatorFactory">
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="fixedFineAmountCalculator" ref="fixedFineAmountCalculator"/>
        <constructor-arg name="zeroFineAmountCalculator" ref="zeroFineAmountCalculator"/>
        <constructor-arg name="fineAmountCalculator10205" ref="fineAmountCalculator10205"/>
        <constructor-arg name="perDayFineAmountCalculator" ref="perDayFineAmountCalculator"/>
        <constructor-arg name="fineAmountCalculator10350" ref="fineAmountCalculator10350"/>
        <constructor-arg name="fineAmountCalculator10360" ref="fineAmountCalculator10360"/>
    </bean>

    <bean id="marksComputationFactory"
          class="com.embrate.cloud.core.lib.examination.marks.computation.MarksComputationFactory">
        <constructor-arg name="sumComputator" ref="sumComputator"/>
        <constructor-arg name="bestOfAllComputator" ref="bestOfAllComputator"/>
        <constructor-arg name="sumOfBestNComputator" ref="sumOfBestNComputator"/>
    </bean>

    <bean id="sumComputator"
          class="com.embrate.cloud.core.lib.examination.marks.computation.SumComputator">
    </bean>

    <bean id="bestOfAllComputator"
          class="com.embrate.cloud.core.lib.examination.marks.computation.BestOfAllComputator">
    </bean>

    <bean id="sumOfBestNComputator"
          class="com.embrate.cloud.core.lib.examination.marks.computation.SumOfBestNComputator">
    </bean>

    <bean id="msg91SMSWebhookManager" class="com.lernen.cloud.core.lib.sms.MSG91SMSWebhookManager">
        <constructor-arg name="notificationManager" ref="notificationManager"/>
    </bean>

    <bean id="notificationManager"
          class="com.lernen.cloud.core.lib.notifications.NotificationManager">
        <constructor-arg name="notificationStatusDao" ref="notificationStatusDao"/>
    </bean>


    <bean id="basicStudentAttendanceCalculator"
          class="com.lernen.cloud.core.lib.attendance.student.BasicStudentAttendanceCalculator">
    </bean>

    <bean id="studentAttendanceCalculatorFactory"
          class="com.lernen.cloud.core.lib.attendance.student.StudentAttendanceCalculatorFactory">
        <constructor-arg name="basicStudentAttendanceCalculator" ref="basicStudentAttendanceCalculator"/>
    </bean>

    <bean id="attendanceManager" class="com.lernen.cloud.core.lib.attendance.AttendanceManager">
        <constructor-arg name="attendanceDao" ref="attendanceDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userAttendanceDao" ref="userAttendanceDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="studentAttendanceCalculatorFactory" ref="studentAttendanceCalculatorFactory"/>
        <constructor-arg name="holidayCalendarManager" ref="holidayCalendarManager"/>
        <constructor-arg name="studentDao" ref="studentDao"/>
    </bean>

    <bean id="staffAttendanceManager" class="com.lernen.cloud.core.lib.attendance.staff.StaffAttendanceManager">
        <constructor-arg name="staffAttendanceDao" ref="staffAttendanceDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userAttendanceDao" ref="userAttendanceDao"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="staffDao" ref="staffDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="holidayCalendarManager" ref="holidayCalendarManager"/>
    </bean>

    <bean id="trackingEventsManager"
          class="com.lernen.cloud.core.lib.tracking.events.TrackingEventsManager">
        <constructor-arg name="trackingEventsDao" ref="trackingEventsDao"/>
    </bean>

    <bean id="staffManager" class="com.lernen.cloud.core.lib.staff.StaffManager">
        <constructor-arg name="staffDao" ref="staffDao"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="transportConfigurationDao" ref="transportConfigurationDao"/>
        <constructor-arg name="staffAttendanceDao" ref="staffAttendanceDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="incomeExpenseManager" class="com.lernen.cloud.core.lib.incomeexpense.IncomeExpenseManager">
        <constructor-arg name="incomeExpenseDao" ref="incomeExpenseDao"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="feePaymentManager" ref="feePaymentManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
    </bean>

    <bean id="userPermissionManager" class="com.lernen.cloud.core.lib.permissions.UserPermissionManager">
        <constructor-arg name="userPermissionDao" ref="userPermissionDao"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="incomeExpenseReportGenerator"
          class="com.lernen.cloud.core.lib.incomeexpense.IncomeExpenseReportGenerator">
        <constructor-arg name="incomeExpenseManager" ref="incomeExpenseManager"/>
        <constructor-arg name="feePaymentInsightManager" ref="feePaymentInsightManager"/>
        <constructor-arg name="userWalletManager" ref="userWalletManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="inventoryTransactionsManager" ref="inventoryTransactionsManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
    </bean>

    <bean id="examReportGenerator"
          class="com.lernen.cloud.core.lib.examination.ExamReportGenerator">
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="courseManager" ref="courseManager"/>
        <constructor-arg name="examinationManager" ref="examinationManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="timetableManager" ref="timetableManager"/>
        <constructor-arg name="examReportCardManager" ref="examReportCardManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="rankCalculatorFactory" ref="rankCalculatorFactory"/>
    </bean>

    <bean id="examGraphicalReportGenerator"
          class="com.lernen.cloud.core.lib.examination.ExamGraphicalReportGenerator">
        <constructor-arg name="courseManager" ref="courseManager"/>
        <constructor-arg name="examinationManager" ref="examinationManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="examReportCardManager" ref="examReportCardManager"/>
        <constructor-arg name="rankCalculatorFactory" ref="rankCalculatorFactory"/>
    </bean>

    <bean id="studentAuditLogWriter" class="com.lernen.cloud.core.lib.audit.log.StudentAuditLogWriter">
        <constructor-arg name="auditLogDao" ref="auditLogDao"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="feeAuditLogWriter" class="com.lernen.cloud.core.lib.audit.log.FeeAuditLogWriter">
        <constructor-arg name="auditLogDao" ref="auditLogDao"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="auditLogManager" class="com.lernen.cloud.core.lib.audit.log.AuditLogManager">
        <constructor-arg name="auditLogDao" ref="auditLogDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager"/>
        <constructor-arg name="feePaymentManager" ref="feePaymentManager"/>
    </bean>

    <bean id="clientPaymentManager" class="com.lernen.cloud.core.lib.client.payment.ClientPaymentManager">
        <constructor-arg name="clientPaymentDao" ref="clientPaymentDao"/>
    </bean>

    <bean id="googleRecaptchaManager" class="com.lernen.cloud.core.lib.google.auth.GoogleRecaptchaManager">
        <constructor-arg name="restClient" ref="restClient"/>
    </bean>

    <bean id="studentRegistrationManager" class="com.lernen.cloud.core.lib.student.StudentRegistrationManager">
        <constructor-arg name="studentRegistrationDao" ref="studentRegistrationDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="googleRecaptchaManager" ref="googleRecaptchaManager"/>
        <constructor-arg name="studentAdmissionManager" ref="studentAdmissionManager"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager"/>
        <constructor-arg name="paymentGatewayManager" ref="paymentGatewayManager"/>
        <constructor-arg name="environmentPropertyProvider" ref="environmentPropertyProvider"/>
        <constructor-arg name="onlineStudentRegistrationFeesFactory" ref="onlineStudentRegistrationFeesFactory"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="feePaymentManager" ref="feePaymentManager"/>
    </bean>


    <bean id="examGreenSheetManager" class="com.lernen.cloud.core.lib.examination.ExamGreenSheetManager">
        <constructor-arg name="examinationManager" ref="examinationManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="courseManager" ref="courseManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="examGreenSheetHandler" class="com.lernen.cloud.core.lib.examination.ExamGreenSheetHandler">
        <constructor-arg name="examGreenSheetManager" ref="examGreenSheetManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="examReportCardManager" ref="examReportCardManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="userWalletManager" class="com.embrate.cloud.core.lib.wallet.UserWalletManager">
        <constructor-arg name="userWalletDao" ref="userWalletDao"/>
        <constructor-arg name="userDao" ref="userDao"/>
        <constructor-arg name="studentDao" ref="studentDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
    </bean>

    <bean id="salaryConfigurationManager" class="com.embrate.cloud.core.lib.salary.SalaryConfigurationManager">
        <constructor-arg name="salaryDao" ref="salaryDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="staffDao" ref="staffDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>
    <bean id="lectureManager" class="com.embrate.cloud.core.lib.lecture.LectureManager">
        <constructor-arg name="lectureDao" ref="lectureDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="courseManager" ref="courseManager"/>
    </bean>

    <bean id="discussionBoardManager" class="com.embrate.cloud.core.lib.discussionboard.DiscussionBoardManager">
        <constructor-arg name="discussionBoardDao" ref="discussionBoardDao"/>
        <constructor-arg name="userManager" ref="userManager"/>
    </bean>

    <bean id="mobileApplicationManager" class="com.lernen.cloud.core.lib.application.mobile.MobileApplicationManager">
        <constructor-arg name="helpAndSupportManager" ref="helpAndSupportManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="mobileConfigurationManager"
          class="com.lernen.cloud.core.lib.application.mobile.MobileConfigurationManager">
        <constructor-arg name="feePaymentManager" ref="feePaymentManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="configurationManager" ref="configurationManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="helpAndSupportManager" class="com.lernen.cloud.core.lib.application.mobile.HelpAndSupportManager">
    </bean>

    <bean id="attendanceReportGenerator" class="com.lernen.cloud.core.lib.attendance.AttendanceReportGenerator">
        <constructor-arg name="attendanceManager" ref="attendanceManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="holidayCalendarManager" ref="holidayCalendarManager"/>
    </bean>

    <bean id="staffAttendanceReportGenerator"
          class="com.lernen.cloud.core.lib.attendance.staff.StaffAttendanceReportGenerator">
        <constructor-arg name="staffAttendanceManager" ref="staffAttendanceManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="holidayCalendarManager" ref="holidayCalendarManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="lectureReportGenerator" class="com.embrate.cloud.core.lib.lecture.LectureReportGenerator">
        <constructor-arg name="lectureManager" ref="lectureManager"/>
    </bean>

    <bean id="homeworkManager" class="com.embrate.cloud.core.lib.homework.HomeworkManager">
        <constructor-arg name="homeworkDao" ref="homeworkDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="courseManager" ref="courseManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="timetableManager" ref="timetableManager"/>
    </bean>

    <bean id="fcmServiceInstanceFactory" class="com.embrate.cloud.core.lib.service.fcm.FCMServiceInstanceFactory">
    </bean>

    <bean id="firebaseMessagingServiceHandler"
          class="com.embrate.cloud.core.lib.service.fcm.FirebaseMessagingServiceHandler">
        <constructor-arg name="fcmServiceInstanceFactory" ref="fcmServiceInstanceFactory"/>
    </bean>

    <bean id="pushNotificationManager" class="com.embrate.cloud.core.lib.push.notification.PushNotificationManager">
        <constructor-arg name="pushNotificationServiceHandler" ref="firebaseMessagingServiceHandler"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="pushNotificationDao" ref="pushNotificationDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
    </bean>

    <bean id="noticeBoardManager" class="com.embrate.cloud.core.lib.noticeboard.NoticeBoardManager">
        <constructor-arg name="noticeBoardDao" ref="noticeBoardDao"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
    </bean>

    <bean id="instituteOnBoardingManager"
          class="com.embrate.cloud.core.lib.institute.onboarding.InstituteOnBoardingManager">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="studentAdmissionManager" ref="studentAdmissionManager"/>
        <constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="configurationManager" ref="configurationManager"/>
        <constructor-arg name="s3FileSystem" ref="s3FileSystem"/>
        <constructor-arg name="env" value="${lernen_env}"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="instituteSetupManager" ref="instituteSetupManager"/>
    </bean>

    <bean id="instituteSetupManager" class="com.embrate.cloud.core.lib.institute.onboarding.InstituteSetupManager">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="moduleSetupProcessorRegistry" ref="moduleSetupProcessorRegistry"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="configurationManager" ref="configurationManager"/>
    </bean>

    <bean id="moduleSetupProcessorRegistry"
          class="com.embrate.cloud.core.lib.institute.onboarding.step.ModuleSetupProcessorRegistry">
        <constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager"/>
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="configurationManager" ref="configurationManager"/>
        <constructor-arg name="examinationManager" ref="examinationManager"/>
        <constructor-arg name="inventoryOutletManager" ref="inventoryOutletManager"/>
        <constructor-arg name="leaveConfigurationManager" ref="leaveConfigurationManager"/>
        <constructor-arg name="salaryConfigurationManager" ref="salaryConfigurationManager"/>
        <constructor-arg name="complainBoxManager" ref="complainBoxManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
    </bean>

    <bean id="s3FileSystem" class="com.embrate.cloud.core.lib.filesystem.S3FileSystem">
        <constructor-arg name="s3ClientProvider" ref="s3ClientProvider"/>
        <constructor-arg name="s3AccessEnable" value="true"/>
    </bean>

    <bean id="communicationServiceManager"
          class="com.embrate.cloud.core.lib.service.communication.CommunicationServiceManager">
        <constructor-arg name="notificationStatusDao" ref="notificationStatusDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
    </bean>

    <bean id="notificationTemplateManager"
          class="com.embrate.cloud.core.lib.templates.notification.NotificationTemplateManager">
        <constructor-arg name="s3FileSystem" ref="s3FileSystem"/>
        <constructor-arg name="templateDao" ref="communicationTemplateDao"/>
        <constructor-arg name="s3BucketName" value="lernen-assets-v5"/>
    </bean>


    <bean id="timetableManager" class="com.embrate.cloud.core.lib.timetable.TimetableManager">
        <constructor-arg name="timetableDao" ref="timetableDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="courseManager" ref="courseManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="studyTrackerManager" class="com.lernen.cloud.core.lib.studytracker.StudyTrackerManager">
        <constructor-arg name="studyTrackerDao" ref="studyTrackerDao"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>


    <bean id="feeReportDetailsGenerator"
          class="com.lernen.cloud.core.lib.fees.payment.FeeReportDetailsGenerator">
        <constructor-arg name="feePaymentInsightManager" ref="feePaymentInsightManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager"/>
        <constructor-arg name="feeDiscountConfigurationManager" ref="feeDiscountConfigurationManager"/>
        <constructor-arg name="userWalletReportGenerator" ref="userWalletReportGenerator"/>
    </bean>

    <bean id="staffReportGenerator"
          class="com.lernen.cloud.core.lib.staff.StaffReportGenerator">
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="excelReportGenerator" class="com.lernen.cloud.core.lib.reports.ExcelReportGenerator">
    </bean>

    <bean id="cashFreePaymentGatewayService"
          class="com.embrate.cloud.core.lib.service.payment.gateway.cashfree.CashFreePaymentGatewayService">
        <constructor-arg name="restClient" ref="restClient"/>
    </bean>

    <bean id="atomPaymentGatewayService"
          class="com.embrate.cloud.core.lib.service.payment.gateway.atom.AtomPaymentGatewayService">
        <constructor-arg name="restClient" ref="restClient"/>
    </bean>

    <bean id="jodoPaymentGatewayService"
          class="com.embrate.cloud.core.lib.service.payment.gateway.jodo.JodoPaymentGatewayService">
        <constructor-arg name="restClient" ref="restClient"/>
        <constructor-arg name="restAPIHandler" ref="restAPIHandler"/>
    </bean>

    <bean id="jodoPaymentGatewayTransactionHandler"
          class="com.embrate.cloud.core.lib.service.payment.gateway.jodo.JodoPaymentGatewayTransactionHandler">
        <constructor-arg name="restAPIHandler" ref="restAPIHandler"/>
    </bean>

    <bean id="razorpayPaymentGatewayService"
          class="com.embrate.cloud.core.lib.service.payment.gateway.razorpay.RazorpayPaymentGatewayService">
    </bean>

    <!--	<constructor-arg name="clientId" value="676283bbb9632a16bb497140882676" />-->
    <!--	<constructor-arg name="clientSecret" value="7b5d53a776da89835fb9661f69fa01fee7ba102f" />-->

    <bean id="paymentGatewayServiceProviderFactory"
          class="com.embrate.cloud.core.lib.service.payment.gateway.PaymentGatewayServiceProviderFactory">
        <constructor-arg name="cashFreePaymentGatewayService" ref="cashFreePaymentGatewayService"/>
        <constructor-arg name="atomPaymentGatewayService" ref="atomPaymentGatewayService"/>
        <constructor-arg name="jodoPaymentGatewayService" ref="jodoPaymentGatewayService"/>
        <constructor-arg name="razorpayPaymentGatewayService" ref="razorpayPaymentGatewayService"/>
    </bean>

    <bean id="paymentGatewayTransactionHandlerFactory"
          class="com.embrate.cloud.core.lib.service.payment.gateway.PaymentGatewayTransactionHandlerFactory">
        <constructor-arg name="cashFreePaymentGatewayService" ref="cashFreePaymentGatewayService"/>
        <constructor-arg name="webhookSecret" value="${razorpay_webhook_secret}"/>
    </bean>

    <bean id="paymentGatewayManager" class="com.embrate.cloud.core.lib.service.payment.gateway.PaymentGatewayManager">
        <constructor-arg name="paymentGatewayServiceProviderFactory" ref="paymentGatewayServiceProviderFactory"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="userWalletManager" ref="userWalletManager"/>
        <constructor-arg name="paymentGatewayMerchantManager" ref="paymentGatewayMerchantManager"/>
        <constructor-arg name="paymentGatewayTransactionHandlerFactory" ref="paymentGatewayTransactionHandlerFactory"/>
        <constructor-arg name="feePaymentManager" ref="feePaymentManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="paymentGatewayTransactionsDao" ref="paymentGatewayTransactionsDao"/>
        <constructor-arg name="paymentGatewayTaskHandlerFactory" ref="paymentGatewayTaskHandlerFactory"/>
        <constructor-arg name="paymentGatewayWebhookProvider" ref="paymentGatewayWebhookProvider"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="restAPIHandler" ref="restAPIHandler"/>
        <constructor-arg name="studentRegistrationDao" ref="studentRegistrationDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="paymentGatewayWebhookManager"
          class="com.embrate.cloud.core.lib.service.payment.gateway.PaymentGatewayWebhookManager">
        <constructor-arg name="paymentGatewayManager" ref="paymentGatewayManager"/>
    </bean>

    <bean id="paymentGatewayMerchantManager"
          class="com.embrate.cloud.core.lib.service.payment.gateway.merchant.PaymentGatewayMerchantManager">
        <constructor-arg name="paymentGatewayMerchantDetailsDao" ref="paymentGatewayMerchantDetailsDao"/>
    </bean>

    <bean id="studentFeePaymentTaskHandler"
          class="com.embrate.cloud.core.lib.service.payment.gateway.StudentFeePaymentTaskHandler">
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="feePaymentManager" ref="feePaymentManager"/>
        <constructor-arg name="userWalletManager" ref="userWalletManager"/>
    </bean>

    <bean id="studentPortalManager" class="com.embrate.cloud.core.lib.portal.student.StudentPortalManager">
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="feePaymentManager" ref="feePaymentManager"/>
        <constructor-arg name="lectureManager" ref="lectureManager"/>
        <constructor-arg name="homeworkManager" ref="homeworkManager"/>
        <constructor-arg name="pushNotificationManager" ref="pushNotificationManager"/>
    </bean>

    <bean id="userWalletRechargeTaskHandler"
          class="com.embrate.cloud.core.lib.service.payment.gateway.UserWalletRechargeTaskHandler">
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="userWalletManager" ref="userWalletManager"/>
    </bean>

    <bean id="studentRegistrationTaskHandler"
          class="com.embrate.cloud.core.lib.service.payment.gateway.StudentRegistrationTaskHandler">
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="userWalletManager" ref="userWalletManager"/>
        <constructor-arg name="studentRegistrationDao" ref="studentRegistrationDao"/>
    </bean>


    <bean id="paymentGatewayTaskHandlerFactory"
          class="com.embrate.cloud.core.lib.service.payment.gateway.PaymentGatewayTaskHandlerFactory">
        <constructor-arg name="studentFeePaymentTaskHandler" ref="studentFeePaymentTaskHandler"/>
        <constructor-arg name="userWalletRechargeTaskHandler" ref="userWalletRechargeTaskHandler"/>
        <constructor-arg name="studentRegistrationTaskHandler" ref="studentRegistrationTaskHandler"/>
    </bean>

    <bean id="paymentGatewayWebhookProvider"
          class="com.embrate.cloud.core.lib.service.payment.gateway.PaymentGatewayWebhookProvider">
        <constructor-arg name="environmentPropertyProvider" ref="environmentPropertyProvider"/>
    </bean>

    <bean lazy-init="true" id="awsSimpleDBPropertyStore"
          class="com.lernen.cloud.core.utils.store.AWSSimpleDBPropertyStore">
        <constructor-arg name="simpleDBClientProvider" ref="simpleDBClientProvider"/>
        <constructor-arg name="kmsClient" ref="envPropertiesKMSClient"/>
        <constructor-arg name="env" value="${lernen_env}"/>
    </bean>

    <bean id="transportTrackingManger" class="com.embrate.cloud.core.lib.transport.tracking.TransportTrackingManger">
        <constructor-arg name="transportTrackingDao" ref="transportTrackingDao"/>
        <constructor-arg name="transportAssignmentManager" ref="transportAssignmentManager"/>
        <constructor-arg name="transportConfigurationManager" ref="transportConfigurationManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="rewardManager"
          class="com.embrate.cloud.core.lib.scratchcard.rewards.RewardManager">
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="feePaymentTransactionDao" ref="feePaymentTransactionDao"/>
    </bean>

    <bean id="scratchCardManager" class="com.embrate.cloud.core.lib.scratchcard.ScratchCardManager">
        <constructor-arg name="rewardManager" ref="rewardManager"/>
        <constructor-arg name="scratchCardDao" ref="scratchCardDao"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="userWalletManager" ref="userWalletManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
    </bean>


    <bean id="userWalletReportsManager"
          class="com.embrate.cloud.core.lib.wallet.UserWalletReportsManager">
        <constructor-arg name="userWalletManager" ref="userWalletManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
    </bean>

    <bean id="userWalletReportGenerator"
          class="com.embrate.cloud.core.lib.wallet.UserWalletReportGenerator">
        <constructor-arg name="userWalletManager" ref="userWalletManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>


    <!--	This is required for init the velocity engine for validation of templates-->
    <bean id="validationVelocityLoader" class="com.lernen.cloud.core.utils.VelocityLoader"
          init-method="init">
        <property name="strictReferencesCheck" value="true"/>
    </bean>


    <bean id="excelNotificationManager" class="com.lernen.cloud.core.lib.notifications.excel.ExcelNotificationManager">
        <constructor-arg name="s3FileSystem" ref="s3FileSystem"/>
        <constructor-arg name="templateDao" ref="communicationTemplateDao"/>
        <constructor-arg name="s3BucketName" value="lernen-assets-v5"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="holidayCalendarManager"
          class="com.embrate.cloud.core.lib.calendar.holiday.HolidayCalendarManager">
        <constructor-arg name="holidayCalendarDao" ref="holidayCalendarDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentDao" ref="studentDao"/>
        <constructor-arg name="staffDao" ref="staffDao"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
    </bean>

    <bean id="inventoryReportGenerationManager"
          class="com.embrate.cloud.core.lib.inventory.v2.InventoryReportGenerationManager">
        <constructor-arg name="inventoryProductManager" ref="inventoryProductManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="inventoryTransactionsManager" ref="inventoryTransactionsManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="salaryReportGenerationManager"
          class="com.embrate.cloud.core.lib.salary.SalaryReportGenerationManager">
        <constructor-arg name="salaryConfigurationManager" ref="salaryConfigurationManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
    </bean>

    <bean id="userAttendanceManager"
          class="com.embrate.cloud.core.lib.attendance.UserAttendanceManager">
        <constructor-arg name="userAttendanceDao" ref="userAttendanceDao"/>
        <constructor-arg name="attendanceComputerFactory" ref="attendanceComputerFactory"/>
        <constructor-arg name="attendanceServiceProviderFactory" ref="attendanceServiceProviderFactory"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="attendanceDeviceManager" ref="attendanceDeviceManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="attendanceDao" ref="attendanceDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="holidayCalendarManager" ref="holidayCalendarManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
    </bean>

    <bean id="attendanceComputerFactory"
          class="com.embrate.cloud.core.lib.attendance.computer.AttendanceComputerFactory">
        <constructor-arg name="basicStudentConcurrentAttendanceComputer"
                         ref="basicStudentConcurrentAttendanceComputer"/>
        <constructor-arg name="basicStaffAttendanceComputer" ref="basicStaffAttendanceComputer"/>
    </bean>


    <bean id="basicStudentAttendanceComputer"
          class="com.embrate.cloud.core.lib.attendance.computer.student.BasicStudentAttendanceComputer">
        <constructor-arg name="attendanceDao" ref="attendanceDao"/>
        <constructor-arg name="userAttendanceDao" ref="userAttendanceDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
    </bean>

    <bean id="basicStudentConcurrentAttendanceComputer"
          class="com.embrate.cloud.core.lib.attendance.computer.student.BasicStudentConcurrentAttendanceComputer">
        <constructor-arg name="attendanceDao" ref="attendanceDao"/>
        <constructor-arg name="userAttendanceDao" ref="userAttendanceDao"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
    </bean>

    <bean id="basicStaffAttendanceComputer"
          class="com.embrate.cloud.core.lib.attendance.computer.staff.BasicStaffAttendanceComputer">
        <constructor-arg name="staffAttendanceManager" ref="staffAttendanceManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>


    <bean id="attendanceServiceProviderFactory"
          class="com.embrate.cloud.core.lib.attendance.service.AttendanceServiceProviderFactory">
        <constructor-arg name="camsUnitDeviceServiceProvider" ref="camsUnitDeviceServiceProvider"/>
        <constructor-arg name="mantraDeviceServiceProvider" ref="mantraDeviceServiceProvider"/>
    </bean>

    <bean id="camsUnitDeviceServiceProvider"
          class="com.embrate.cloud.core.lib.attendance.service.camsunit.CamsUnitDeviceServiceProvider">
        <constructor-arg name="restClient" ref="restClient"/>
    </bean>

    <bean id="mantraDeviceServiceProvider"
          class="com.embrate.cloud.core.lib.attendance.service.mantra.MantraDeviceServiceProvider">
    </bean>

    <bean id="attendanceDeviceManager"
          class="com.embrate.cloud.core.lib.attendance.device.AttendanceDeviceManager">
        <constructor-arg name="attendanceDeviceDao" ref="attendanceDeviceDao"/>
    </bean>

    <bean id="camsUnitDeviceManager"
          class="com.embrate.cloud.core.lib.attendance.service.camsunit.CamsUnitDeviceManager">
        <constructor-arg name="userAttendanceManager" ref="userAttendanceManager"/>
    </bean>

    <bean id="mantraDeviceManager"
          class="com.embrate.cloud.core.lib.attendance.service.mantra.MantraDeviceManager">
        <constructor-arg name="userAttendanceManager" ref="userAttendanceManager"/>
    </bean>

    <bean id="frontDeskManager" class="com.embrate.cloud.core.lib.frontdesk.FrontDeskManager">
        <constructor-arg name="frontDeskDao" ref="frontDeskDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="transportConfigurationManager" ref="transportConfigurationManager"/>
    </bean>

    <bean id="mobileAppInstituteScopeFactory"
          class="com.lernen.cloud.core.lib.application.mobile.MobileAppInstituteScopeFactory">
    </bean>

    <bean id="institutePreferenceManager"
          class="com.embrate.cloud.core.lib.institute.management.InstitutePreferenceManager">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="configurationManager" ref="configurationManager"/>
        <constructor-arg name="productFeatureRegistry" ref="productFeatureRegistry"/>
    </bean>

    <bean id="attendanceFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.AttendanceFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="examAdmitCardFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.ExamAdmitCardFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="examinationFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.ExaminationFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="libraryFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.LibraryFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="studyTrackerFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.StudyTrackerFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="feesFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.FeesFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="generalProductFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.GeneralProductFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>


    <bean id="inventoryFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.InventoryFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="studentFinanceFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.StudentFinanceFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="walletFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.WalletFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="mobileAppFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.MobileAppFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="paymentGatewayFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.PaymentGatewayFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="rewardFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.RewardFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="smsFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.SMSFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="studentIdentityCardFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.StudentIdentityCardFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="documentPropertiesFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.DocumentPropertiesFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="voiceCallFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.VoiceCallFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="webAppFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.WebAppFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="pushNotificationFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.PushNotificationFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>


    <bean id="productFeatureRegistry"
          class="com.embrate.cloud.core.lib.feature.preference.ProductFeatureRegistry">
        <constructor-arg name="productFeatureHandlers">
            <list>
                <ref bean="generalProductFeatureHandler"/>
                <ref bean="webAppFeatureHandler"/>
                <ref bean="mobileAppFeatureHandler"/>
                <ref bean="feesFeatureHandler"/>
                <ref bean="smsFeatureHandler"/>
                <ref bean="voiceCallFeatureHandler"/>
                <ref bean="attendanceFeatureHandler"/>
                <ref bean="examAdmitCardFeatureHandler"/>
                <ref bean="examinationFeatureHandler"/>
                <ref bean="inventoryFeatureHandler"/>
                <ref bean="studentFinanceFeatureHandler"/>
                <ref bean="walletFeatureHandler"/>
                <ref bean="paymentGatewayFeatureHandler"/>
                <ref bean="studentIdentityCardFeatureHandler"/>
                <ref bean="studyTrackerFeatureHandler"/>
                <ref bean="rewardFeatureHandler"/>
                <ref bean="pushNotificationFeatureHandler"/>
                <ref bean="staffAttendanceFeatureHandler"/>
                <ref bean="documentPropertiesFeatureHandler"/>
                <ref bean="libraryFeatureHandler"/>
                <ref bean="whatsappFeatureHandler"/>
            </list>
        </constructor-arg>
    </bean>

    <bean id="timetableReportGenerator" class="com.embrate.cloud.core.lib.timetable.TimetableReportGenerator">
        <constructor-arg name="timetableManager" ref="timetableManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
    </bean>

    <bean id="dashboardManager" class="com.embrate.cloud.core.lib.dashboards.DashboardManager">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="attendanceManager" ref="attendanceManager"/>
        <constructor-arg name="feePaymentManager" ref="feePaymentManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="transportAssignmentManager" ref="transportAssignmentManager"/>
    </bean>

    <bean id="bulkDocumentManager" class="com.embrate.cloud.core.lib.document.BulkDocumentManager">
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
    </bean>


    <bean id="defaultPaymentDiscountCalculator"
          class="com.embrate.cloud.core.lib.payment.DefaultPaymentDiscountCalculator">
    </bean>

    <bean id="paymentDiscountCalculator10205" class="com.embrate.cloud.core.lib.payment.PaymentDiscountCalculator10205">
        <constructor-arg name="feeConfigurationDao" ref="feeConfigurationDao"/>
        <constructor-arg name="feeDiscountConfigurationDao" ref="feeDiscountConfigurationDao"/>
    </bean>

    <bean id="paymentDiscountCalculatorFactory"
          class="com.embrate.cloud.core.lib.payment.PaymentDiscountCalculatorFactory">
        <constructor-arg name="defaultFeePaymentDiscountCalculator" ref="defaultPaymentDiscountCalculator"/>
        <constructor-arg name="paymentDiscountCalculator10205" ref="paymentDiscountCalculator10205"/>
    </bean>

    <bean id="paymentDiscountManager" class="com.embrate.cloud.core.lib.payment.PaymentDiscountManager">
        <constructor-arg name="paymentDiscountCalculatorFactory" ref="paymentDiscountCalculatorFactory"/>
    </bean>

    <bean id="paymentFineManager" class="com.embrate.cloud.core.lib.payment.PaymentFineManager">
        <constructor-arg name="fineAmountCalculatorFactory" ref="fineAmountCalculatorFactory"/>
    </bean>

    <bean id="leaveConfigurationManager"
          class="com.embrate.cloud.core.lib.leave.management.LeaveConfigurationManager">
        <constructor-arg name="leaveTypeDao" ref="leaveTypeDao"/>
        <constructor-arg name="leavePolicyTemplateDao" ref="leavePolicyTemplateDao"/>
    </bean>

    <bean id="userLeavePolicyManager"
          class="com.embrate.cloud.core.lib.leave.management.UserLeavePolicyManager">
        <constructor-arg name="userLeavePolicyDao" ref="userLeavePolicyDao"/>
        <constructor-arg name="leavePolicyTemplateDao" ref="leavePolicyTemplateDao"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
    </bean>


    <bean id="defaultSalaryPayslipComputer"
          class="com.embrate.cloud.core.lib.salary.computer.DefaultSalaryPayslipComputer">
    </bean>
    <bean id="salaryPayslipWithESICEPFDeductionComputer" class="com.embrate.cloud.core.lib.salary.computer.SalaryPayslipWithESICEPFDeductionComputer">
    </bean>


    <bean id="salaryPayslipComputerFactory"
          class="com.embrate.cloud.core.lib.salary.computer.SalaryPayslipComputerFactory">
        <constructor-arg name="defaultSalaryPayslipComputer" ref="defaultSalaryPayslipComputer"/>
        <constructor-arg name="salaryPayslipWithESICEPFDeductionComputer" ref="salaryPayslipWithESICEPFDeductionComputer"/>
    </bean>

    <bean id="userSalaryManager"
          class="com.embrate.cloud.core.lib.salary.UserSalaryManager">
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="salaryDao" ref="salaryDao"/>
        <constructor-arg name="salaryConfigurationManager" ref="salaryConfigurationManager"/>
        <constructor-arg name="holidayCalendarManager" ref="holidayCalendarManager"/>
        <constructor-arg name="staffAttendanceManager" ref="staffAttendanceManager"/>
        <constructor-arg name="userLeavePolicyManager" ref="userLeavePolicyManager"/>
        <constructor-arg name="salaryPayslipComputerFactory" ref="salaryPayslipComputerFactory"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="examConfigManager" class="com.embrate.cloud.core.lib.examination.ExamConfigManager">
        <constructor-arg name="examinationManager" ref="examinationManager"/>
        <constructor-arg name="courseManager" ref="courseManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
    </bean>

    <bean id="studentLeaveManagementManager"
          class="com.embrate.cloud.core.lib.leave.management.StudentLeaveManagementManager">
        <constructor-arg name="userLeavePolicyDao" ref="userLeavePolicyDao"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="attendanceManager" ref="attendanceManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="holidayCalendarManager" ref="holidayCalendarManager"/>
         <constructor-arg name="userLeaveManagementManager" ref="userLeaveManagementManager"/>
    </bean>
    <bean id="staffLeaveManagementManager"
          class="com.embrate.cloud.core.lib.leave.management.StaffLeaveManagementManager">
        <constructor-arg name="userLeavePolicyDao" ref="userLeavePolicyDao"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="attendanceManager" ref="attendanceManager"/>
        <constructor-arg name="staffManager" ref="staffManager"/>
        <constructor-arg name="holidayCalendarManager" ref="holidayCalendarManager"/>
        <constructor-arg name="userLeaveManagementManager" ref="userLeaveManagementManager"/>
        <constructor-arg name="staffAttendanceManager" ref="staffAttendanceManager"/>
        <constructor-arg name="userSalaryManager" ref="userSalaryManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="salaryConfigurationManager" ref="salaryConfigurationManager"/>
        <constructor-arg name="userLeavePolicyManager" ref="userLeavePolicyManager"/>
        <constructor-arg name="salaryDao" ref="salaryDao"/>
    </bean>
    <bean id="userLeaveManagementManager"
          class="com.embrate.cloud.core.lib.leave.management.UserLeaveManagementManager">
        <constructor-arg name="userLeavePolicyDao" ref="userLeavePolicyDao"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="leaveTypeDao" ref="leaveTypeDao"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="feeSetupUtilityManager"
          class="com.embrate.cloud.core.lib.fees.config.FeeSetupUtilityManager">
        <constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager"/>
        <constructor-arg name="feeDiscountConfigurationManager" ref="feeDiscountConfigurationManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
    </bean>

    <bean id="communicationUtilityManager"
          class="com.embrate.cloud.core.lib.communication.utils.CommunicationUtilityManager">
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="notificationManager" ref="notificationManager"/>
    </bean>

    <bean id="organisationReportGenerator"
          class="com.embrate.cloud.core.lib.organisation.OrganisationReportGenerator">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userManager" ref="userManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="feePaymentInsightManager" ref="feePaymentInsightManager"/>
    </bean>

    <bean id="transportSetupUtilityManager"
          class="com.embrate.cloud.core.lib.transport.TransportSetupUtilityManager">
        <constructor-arg name="transportConfigurationManager" ref="transportConfigurationManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
        <constructor-arg name="transportAssignmentManager" ref="transportAssignmentManager"/>
    </bean>

    <bean id="onlineStudentRegistrationFeesFactory"
          class="com.embrate.cloud.core.lib.student.registration.online.OnlineStudentRegistrationFeesFactory">
        <constructor-arg name="defaultOnlineStudentRegistrationFeesHandler"
                         ref="defaultOnlineStudentRegistrationFeesHandler"/>
    </bean>

    <bean id="defaultOnlineStudentRegistrationFeesHandler"
          class="com.embrate.cloud.core.lib.student.registration.online.DefaultOnlineStudentRegistrationFeesHandler">
    </bean>

    <bean id="staffAttendanceFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.StaffAttendanceFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="appointmentDetailsManager" class="com.lernen.cloud.core.lib.appointment.AppointmentDetailsManager">
        <constructor-arg name="appointmentDao" ref="appointmentDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
    </bean>

    <bean id="visitorDetailsManager" class="com.lernen.cloud.core.lib.visitor.VisitorDetailsManager">
        <constructor-arg name="visitorDao" ref="visitorDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="documentManager" ref="documentManager"/>
        <constructor-arg name="googleRecaptchaManager" ref="googleRecaptchaManager"/>
        <constructor-arg name="transactionTemplate" ref="transactionTemplate"/>
    </bean>

    <bean id="transportAttendanceManager" class="com.lernen.cloud.core.lib.transport.configuration.TransportAttendanceManager">
        <constructor-arg name="attendanceManager" ref="attendanceManager"/>
        <constructor-arg name="transportAttendanceDao" ref="transportAttendanceDao"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
    </bean>

    <bean id="enquiryDetailsManager" class="com.lernen.cloud.core.lib.enquiry.EnquiryDetailsManager">
        <constructor-arg name="enquiryDetailsDao" ref="enquiryDetailsDao"/>
        <constructor-arg name="googleRecaptchaManager" ref="googleRecaptchaManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="hostelManagementManager" class="com.embrate.cloud.core.lib.hostel.management.HostelManagementManager">
        <constructor-arg name="frontDeskManager" ref="frontDeskManager"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="hostelManagementDao" ref ="hostelManagementDao"/>
    </bean>


    <bean id="hpcFormNavigator" class="com.embrate.cloud.core.lib.hpc.HPCFormNavigator">
    </bean>

    <bean id="hpcFormHelper" class="com.embrate.cloud.core.lib.hpc.HPCFormHelper">
        <constructor-arg name="hpcFormNavigator" ref="hpcFormNavigator" />
    </bean>

    <bean id="hpcConfigurationManager" class="com.embrate.cloud.core.lib.hpc.HPCConfigurationManager">
        <constructor-arg name="hpcConfigurationDao" ref="hpcConfigurationDao" />
        <constructor-arg name="instituteManager" ref="instituteManager" />
        <constructor-arg name="studentManager" ref="studentManager" />
        <constructor-arg name="hpcFormHelper" ref="hpcFormHelper" />
        <constructor-arg name="hpcFormDao" ref="hpcFormDao" />
    </bean>

    <bean id="hpcFormManager" class="com.embrate.cloud.core.lib.hpc.HPCFormManager">
        <constructor-arg name="hpcFormDao" ref="hpcFormDao" />
        <constructor-arg name="hpcConfigurationManager" ref="hpcConfigurationManager" />
        <constructor-arg name="studentManager" ref="studentManager" />
        <constructor-arg name="hpcFormHelper" ref="hpcFormHelper" />
        <constructor-arg name="hpcFormNavigator" ref="hpcFormNavigator" />
        <constructor-arg name="documentManager" ref="documentManager" />
        <constructor-arg name="examinationManager" ref="examinationManager" />
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
    </bean>

    <bean id="transportVehicleTransactionManager" class="com.lernen.cloud.core.lib.transport.configuration.TransportVehicleTransactionManager">
        <constructor-arg name="transportVehicleTransactionDao" ref="transportVehicleTransactionDao" />
    </bean>

    <bean id="whatsappFeatureHandler"
          class="com.embrate.cloud.core.lib.feature.preference.handler.WhatsappFeatureHandler">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>

    <bean id="rankCalculatorFactory"
          class="com.lernen.clould.core.lib.examination.rankrule.RankCalculatorFactory">
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="continuousRankCalculator" ref="continuousRankCalculator"/>
        <constructor-arg name="skipRankCalculator" ref="skipRankCalculator"/>
    </bean>

    <bean id="continuousRankCalculator"
          class="com.lernen.clould.core.lib.examination.rankrule.ContinuousRankCalculator">
    </bean>

    <bean id="skipRankCalculator"
          class="com.lernen.clould.core.lib.examination.rankrule.SkipRankCalculator">
    </bean>

    <bean id="homeworkReportGenerator"
          class="com.embrate.cloud.core.lib.homework.HomeworkReportGenerator">
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="homeworkManager" ref="homeworkManager"/>
        <constructor-arg name="instituteManager" ref="instituteManager"/>
    </bean>
    <bean id="studentFinanceManager"
          class="com.embrate.cloud.core.lib.student.finance.StudentFinanceManager">
        <constructor-arg name="studentFinanceDao" ref="studentFinanceDao"/>
        <constructor-arg name="studentManager" ref="studentManager"/>
        <constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings"/>
        <constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
        <constructor-arg name="userWalletManager" ref="userWalletManager"/>

    </bean>

    <bean id="userDashboardManager"
          class="com.embrate.cloud.core.lib.dashboards.admission.UserDashboardManager">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userDashboardDao" ref="userDashboardDao"/>
        <constructor-arg name="transportAssignmentManager" ref="transportAssignmentManager"/>
    </bean>

    <bean id="attendanceDashboardManager"
          class="com.embrate.cloud.core.lib.dashboards.attendance.AttendanceDashboardManager">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="userDashboardDao" ref="userDashboardDao"/>
        <constructor-arg name="attendanceDashboardDao" ref="attendanceDashboardDao"/>
        <constructor-arg name="attendanceManager" ref="attendanceManager"/>
    </bean>

    <bean id="feesDashboardManager"
          class="com.embrate.cloud.core.lib.dashboards.fees.FeesDashboardManager">
        <constructor-arg name="instituteManager" ref="instituteManager"/>
        <constructor-arg name="feesDashboardDao" ref="feesDashboardDao"/>
    </bean>


    <context:component-scan base-package="com.lernen.cloud.core.lib, com.embrate.cloud.core.lib"/>
    <!-- <aop:aspectj-autoproxy proxy-target-class="true" /> -->
</beans>
