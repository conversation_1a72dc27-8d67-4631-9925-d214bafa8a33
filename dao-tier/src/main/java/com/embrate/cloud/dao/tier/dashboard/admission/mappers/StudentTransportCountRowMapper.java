package com.embrate.cloud.dao.tier.dashboard.admission.mappers;

import com.embrate.cloud.core.api.dashboards.admission.StudentTransportCount;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for StudentTransportCount
 * 
 * <AUTHOR>
 */
public class StudentTransportCountRowMapper implements RowMapper<StudentTransportCount> {

    protected static final String INSTITUTE_ID = "institute_id";
    protected static final String ACADEMIC_SESSION_ID = "academic_session_id";
    protected static final String ACTIVE_STUDENT_COUNT = "active_student_count";

    @Override
    public StudentTransportCount mapRow(ResultSet rs, int rowNum) throws SQLException {
        int instituteId = rs.getInt(INSTITUTE_ID);
        int academicSessionId = rs.getInt(ACADEMIC_SESSION_ID);
        int activeStudentCount = rs.getInt(ACTIVE_STUDENT_COUNT);

        return new StudentTransportCount(instituteId, academicSessionId, activeStudentCount);
    }
}
