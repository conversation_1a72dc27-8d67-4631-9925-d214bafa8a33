package com.lernen.cloud.core.utils;

import com.lernen.cloud.core.api.common.EMonthDay;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

/**
 * Unit test for DateUtils.
 */
public class AppTest
    extends TestCase
{
    /**
     * Create the test case
     *
     * @param testName name of the test case
     */
    public AppTest( String testName )
    {
        super( testName );
    }

    /**
     * @return the suite of tests being tested
     */
    public static Test suite()
    {
        return new TestSuite( AppTest.class );
    }

    /**
     * Test leap day handling in non-leap years
     */
    public void testLeapDayInNonLeapYear()
    {
        // Test February 29th in a non-leap year (2025)
        EMonthDay leapDay = new EMonthDay(29, 2);
        int nonLeapYear = 2025;

        // This should not throw an exception and should return February 28th, 2025
        int result = DateUtils.getDefaultZoneTime(leapDay, nonLeapYear);

        // Verify the result is a valid timestamp
        assertTrue("Result should be positive", result > 0);

        // Verify it's actually February 28th, 2025
        DateTime resultDateTime = new DateTime(result * 1000L, DateTimeZone.forTimeZone(DateUtils.DEFAULT_TIMEZONE));
        assertEquals("Should be February", 2, resultDateTime.getMonthOfYear());
        assertEquals("Should be 28th day", 28, resultDateTime.getDayOfMonth());
        assertEquals("Should be 2025", 2025, resultDateTime.getYear());
    }

    /**
     * Test leap day handling in leap years
     */
    public void testLeapDayInLeapYear()
    {
        // Test February 29th in a leap year (2024)
        EMonthDay leapDay = new EMonthDay(29, 2);
        int leapYear = 2024;

        // This should not throw an exception and should return February 29th, 2024
        int result = DateUtils.getDefaultZoneTime(leapDay, leapYear);

        // Verify the result is a valid timestamp
        assertTrue("Result should be positive", result > 0);

        // Verify it's actually February 29th, 2024
        DateTime resultDateTime = new DateTime(result * 1000L, DateTimeZone.forTimeZone(DateUtils.DEFAULT_TIMEZONE));
        assertEquals("Should be February", 2, resultDateTime.getMonthOfYear());
        assertEquals("Should be 29th day", 29, resultDateTime.getDayOfMonth());
        assertEquals("Should be 2024", 2024, resultDateTime.getYear());
    }

    /**
     * Test normal dates (non-leap day)
     */
    public void testNormalDate()
    {
        // Test a normal date like March 15th
        EMonthDay normalDay = new EMonthDay(15, 3);
        int year = 2025;

        int result = DateUtils.getDefaultZoneTime(normalDay, year);

        // Verify the result is a valid timestamp
        assertTrue("Result should be positive", result > 0);

        // Verify it's actually March 15th, 2025
        DateTime resultDateTime = new DateTime(result * 1000L, DateTimeZone.forTimeZone(DateUtils.DEFAULT_TIMEZONE));
        assertEquals("Should be March", 3, resultDateTime.getMonthOfYear());
        assertEquals("Should be 15th day", 15, resultDateTime.getDayOfMonth());
        assertEquals("Should be 2025", 2025, resultDateTime.getYear());
    }
}
