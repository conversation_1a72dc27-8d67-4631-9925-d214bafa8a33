package com.embrate.cloud.core.api.dashboards.admission;

/**
 * POJO class to hold student transport count data from database queries
 * 
 * <AUTHOR>
 */
public class StudentTransportCount {

    private final int instituteId;
    private final int academicSessionId;
    private final int activeStudentCount;

    public StudentTransportCount(int instituteId, int academicSessionId, int activeStudentCount) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.activeStudentCount = activeStudentCount;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public int getActiveStudentCount() {
        return activeStudentCount;
    }

    @Override
    public String toString() {
        return "StudentTransportCount{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", activeStudentCount=" + activeStudentCount +
                '}';
    }
}
